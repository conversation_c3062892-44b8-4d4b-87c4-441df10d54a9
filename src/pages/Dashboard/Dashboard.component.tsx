import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { AlertTriangle, X } from 'lucide-react'
import MainLayout from '../../components/layout/MainLayout'
import WelcomeSection from './sub-components/WelcomeSection'
import LearningStatsCard from './sub-components/LearningStatsCard'
import PerformanceCard from './sub-components/PerformanceCard'
import LeaderboardCard from './sub-components/LeaderboardCard'

import TodaysThemeCard from './sub-components/TodaysThemeCard'
import { DashboardComponentProps } from './types'

/**
 * Dashboard Component - Enhanced modern UI with improved information hierarchy
 */
const DashboardComponent: React.FC<DashboardComponentProps> = ({
  user,
  stats,
  statsLoading,
  statsError,
  onRefreshStats,
  leaderboard,
  leaderboardLoading,
  leaderboardError,
  onRefreshLeaderboard,
  accessDeniedMessage,
  onDismissAccessDenied
}) => {
  // Minimal animation variants for sleek feel
  const cardVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    }
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        duration: 0.2
      }
    }
  }

  return (
    <MainLayout
      title="Dashboard"
      description={`Welcome back, ${user.full_name || user.username}!`}
    >
      {/* Subtle Background Pattern */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-purple-50/30 dark:from-blue-950/20 dark:via-transparent dark:to-purple-950/20" />
        <div className="absolute top-0 left-0 w-full h-full">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-gradient-to-br from-blue-200/10 to-purple-200/10 dark:from-blue-800/5 dark:to-purple-800/5 rounded-full blur-3xl" />
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-br from-purple-200/10 to-pink-200/10 dark:from-purple-800/5 dark:to-pink-800/5 rounded-full blur-3xl" />
        </div>
      </div>

      {/* Access Denied Message */}
      <AnimatePresence>
        {accessDeniedMessage && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 w-full max-w-md"
          >
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/50 rounded-lg shadow-lg p-4 flex items-start gap-3">
              <AlertTriangle className="w-5 h-5 text-red-600 dark:text-red-400 flex-shrink-0 mt-0.5" />
              <div className="flex-1">
                <h3 className="font-medium text-red-800 dark:text-red-300">Access Denied</h3>
                <p className="text-sm text-red-700 dark:text-red-200">{accessDeniedMessage}</p>
              </div>
              <button
                onClick={onDismissAccessDenied}
                className="p-1 rounded-full hover:bg-red-200/50 dark:hover:bg-red-800/30 text-red-500 dark:text-red-400"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="relative z-10 w-[90%] mx-auto px-4 sm:px-6 lg:px-8 py-4"
      >
        {/* Modern Grid Layout - Left content 65%, Right leaderboard 35% */}
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 h-[calc(100vh-10rem)]">

          {/* Left Side Content - Takes 65% (8 columns) */}
          <div className="lg:col-span-8 flex flex-col gap-4 h-full">
            {/* Welcome Section - Full width within left side */}
            <motion.div
              variants={cardVariants}
            >
              <WelcomeSection user={user} cardVariants={cardVariants} />
            </motion.div>

            {/* Today's Theme Card - Full width within left side */}
            <motion.div
              variants={cardVariants}
            >
              <TodaysThemeCard cardVariants={cardVariants} />
            </motion.div>

            {/* Learning Stats Card - Full width within left side */}
            <motion.div
              variants={cardVariants}
            >
              <LearningStatsCard
                stats={stats}
                loading={statsLoading}
                error={statsError}
                onRefresh={onRefreshStats}
                cardVariants={cardVariants}
              />
            </motion.div>

            {/* Performance Card - Full width within left side */}
            <motion.div
              variants={cardVariants}
            >
              <PerformanceCard
                stats={stats}
                loading={statsLoading}
                error={statsError}
                onRefresh={onRefreshStats}
                cardVariants={cardVariants}
              />
            </motion.div>
          </div>

          {/* Right Side - Leaderboard takes 35% (4 columns) */}
          <motion.div
            className="lg:col-span-4 h-full"
            variants={cardVariants}
          >
            <LeaderboardCard
              leaderboard={leaderboard}
              loading={leaderboardLoading}
              error={leaderboardError}
              onRefresh={onRefreshLeaderboard}
              cardVariants={cardVariants}
            />
          </motion.div>

        </div>
      </motion.div>
    </MainLayout>
  )
}

export default DashboardComponent
