import React from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { useAppSelector } from '../store/hooks'
import { Shield, AlertTriangle } from 'lucide-react'
import { motion } from 'framer-motion'

interface SuperProtectedRouteProps {
  children: React.ReactNode
}

/**
 * SuperProtectedRoute - Protects routes that require admin access
 * Redirects non-admin users to dashboard with access denied message
 */
const SuperProtectedRoute: React.FC<SuperProtectedRouteProps> = ({ children }) => {
  const { user, isLoading } = useAppSelector((state) => state.auth)
  const location = useLocation()

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <h2 className="text-lg font-medium text-foreground mb-2">
            Verifying Access...
          </h2>
          <p className="text-sm text-muted-foreground">
            Checking admin privileges
          </p>
        </div>
      </div>
    )
  }

  // Check if user has admin privileges
  const isAdmin = user?.role === 'admin' || user?.role === 'agent'

  // Redirect non-admin users to dashboard
  if (!isAdmin) {
    return <Navigate to="/dashboard" state={{ 
      accessDenied: true, 
      from: location.pathname,
      message: "This section requires administrator privileges"
    }} replace />
  }

  return <>{children}</>
}

export default SuperProtectedRoute
