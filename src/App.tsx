import React from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { Provider } from 'react-redux'
import { store } from './store'
import { ThemeProvider } from './services/theme/ThemeProvider'
import { NavigationProvider } from './contexts/NavigationContext'
import Home from './pages/Home/Home'
import Login from './pages/Auth/Login'
import Signup from './pages/Auth/Signup'
import Register from './pages/Auth/Register'
import Onboarding from './pages/Onboarding/Onboarding'
import Dashboard from './pages/Dashboard/Dashboard'
import BeginLearning from './pages/BeginLearning/BeginLearning'
import Tasks from './pages/Tasks/Tasks'
import TaskSetDetail from './pages/Tasks/[tasksetid]/TaskSetDetail'
import TaskItem from './pages/Tasks/[tasksetid]/[taskitemid]/TaskItem'
import StoryItem from './pages/Tasks/[tasksetid]/storyitem/[storyitemid]/StoryItem'
import StoryLayout from './pages/Story/[story_id]/StoryLayout'
import StagePage from './pages/Story/[story_id]/[stage]/StagePage'
import AnthologyContainer from './pages/Curated/pages/curated.anthology'
import Atelier from './pages/Curated/pages/curated.atelier'
import Playground from './pages/Curated/pages/curated.playground'
import Editors from './pages/Editors/Editors'
import Users from './pages/UserManagement/Users'
import ProtectedRoute from './components/ProtectedRoute'
import SuperProtectedRoute from './components/SuperProtectedRoute'
import AuthInitializer from './components/AuthInitializer'
import GlobalStoryNotification from './components/common/GlobalStoryNotification'
import AppLayout from './components/layout/AppLayout'
import ResponsiveTestPanel from './components/dev/ResponsiveTestPanel'
import './styles/globals.css'

function App() {
  return (
    <Provider store={store}>
      <ThemeProvider>
        <Router>
          <NavigationProvider>
            <AuthInitializer />
            <GlobalStoryNotification position="top-right" />
            <div className="min-h-screen bg-background text-foreground">
              <Routes>
              {/* Public Routes */}
              <Route path="/" element={<Home />} />
              <Route path="/login" element={<Login />} />
              <Route path="/signup" element={<Signup />} />
              <Route path="/register" element={<Register />} />

              {/* Onboarding route - protected but accessible to new users */}
              <Route path="/onboarding" element={
                <ProtectedRoute>
                  <Onboarding />
                </ProtectedRoute>
              } />

              {/* Protected Routes with Persistent Layout */}
              <Route path="/" element={
                <ProtectedRoute>
                  <AppLayout />
                </ProtectedRoute>
              }>
                <Route path="dashboard" element={<Dashboard />} />
                <Route path="playground" element={<BeginLearning />} />
                <Route path="tasks" element={<Tasks />} />
                <Route path="tasks/:tasksetid" element={<TaskSetDetail />} />
                <Route path="tasks/:tasksetid/taskitem/:taskitemid" element={<TaskItem />} />
                <Route path="tasks/:tasksetid/storyitem/:storyitemid" element={<StoryItem />} />
                {/* Admin-only routes */}
                <Route path="editors" element={
                  <SuperProtectedRoute>
                    <Editors />
                  </SuperProtectedRoute>
                } />
                <Route path="editors-pick" element={
                  <SuperProtectedRoute>
                    <AnthologyContainer />
                  </SuperProtectedRoute>
                } />
                <Route path="themes" element={
                  <SuperProtectedRoute>
                    <Atelier />
                  </SuperProtectedRoute>
                } />
                <Route path="editor-chamber" element={
                  <SuperProtectedRoute>
                    <Playground />
                  </SuperProtectedRoute>
                } />
                <Route path="user-management/users" element={
                  <SuperProtectedRoute>
                    <Users />
                  </SuperProtectedRoute>
                } />
              </Route>

              {/* Story routes - separate layout */}
              <Route path="/story/:story_id" element={
                <ProtectedRoute>
                  <StoryLayout />
                </ProtectedRoute>
              } />

              {/* Default redirect */}
              <Route index element={<Navigate to="/dashboard" replace />} />
              <Route path="*" element={<Navigate to="/dashboard" replace />} />
              </Routes>
            </div>
          </NavigationProvider>

          {/* Development Tools */}
          <ResponsiveTestPanel />
        </Router>
      </ThemeProvider>
    </Provider>
  )
}

export default App
