import { httpBase } from './http/httpBase'

/**
 * Types for Curated Content API
 */
export interface Theme {
  _id: string
  id?: string // For backward compatibility
  name: string
  name_en: string
  description: string
  description_en: string
  category: string
  icon: string
  color?: string // For backward compatibility
  background_color: string
  font_color: string
  is_active: boolean
  created_at: string
  updated_at: string
  statistics?: {
    total_content_sets: number
    total_content_items: number
    average_items_per_set: number
  }
}

export interface CreateThemeRequest {
  name: string
  name_en: string
  description: string
  description_en: string
  category: string
  icon: string
  background_color: string
  font_color: string
  is_active: boolean
}

export interface UpdateThemeRequest extends CreateThemeRequest {}

export interface EngagementQuestion {
  text: string
  text_en: string
  type: string
}

export interface ContentSet {
  _id: string
  id?: string // For backward compatibility
  theme_id: string
  title: string
  title_en: string
  description: string
  description_en: string
  difficulty_level: number
  status: 'pending' | 'completed' | 'in_progress' | 'cancelled'
  gentype: 'primary' | 'follow_up' | 'supplementary' | 'review' | 'curated'
  task_item_ids: string[]
  total_items: number
  created_at: string
  engagement_questions?: EngagementQuestion[]
  theme?: {
    id: string
    name: string
    name_en: string
    icon: string
    color: string
    category: string
  }
}

export interface GeneratedPrompt {
  _id: string
  content: string
  user_id: string
  task_set_id?: string
  created_at: string
  status: 'pending' | 'success' | 'failed'
}

export interface Question {
  question: {
    text: string
    translated_text?: string
    type: 'single_choice' | 'multiple_choice'
    options?: { [key: string]: string }
  }
}

export interface FilterOptions {
  themes: Array<{
    id: string
    name: string
    name_en: string
  }>
  status_options: string[]
  gentype_options: string[]
  difficulty_levels: Array<{
    value: number
    label: string
  }>
}

export interface ThemeFilterOptions {
  categories: string[]
  is_active_options: boolean[]
  sort_options: string[]
  date_filter_info: {
    earliest_date: string
    latest_date: string
    format: string
    quick_filters: Array<{
      label: string
      days?: number
      type?: string
    }>
  }
}

export interface PaginatedResponse<T> {
  data: T[]
  meta: {
    page: number
    limit: number
    total: number
    total_pages: number
  }
}

export interface ApiResponse<T> {
  success: boolean
  data: T
  message: string
  meta: {
    timestamp: string | null
    request_id: string | null
  }
}

/**
 * Query parameters for API requests
 */
export interface ThemesQuery {
  page?: number
  limit?: number
  search?: string
  category?: string
  is_active?: boolean
  start_date?: string
  end_date?: string
  sort_order?: 'asc' | 'desc'
}

export interface ContentQuery {
  page?: number
  limit?: number
  theme_id?: string
  difficulty_level?: number
  status?: string
  gentype?: string
  search?: string
}

/**
 * Curated Content Service
 * Handles all API calls for curated content management
 */
export class CuratedService {
  private static readonly BASE_PATH = '/management/curated'

  /**
   * Clean parameters by removing undefined, null, and empty values
   */
  private static cleanParams(params: any): any {
    const cleaned: any = {}
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value)) {
          const filteredArray = value.filter(v => v !== undefined && v !== null && v !== '')
          if (filteredArray.length > 0) {
            cleaned[key] = filteredArray
          }
        } else {
          cleaned[key] = value
        }
      }
    })
    return cleaned
  }

  /**
   * Get all themes with optional filtering
   */
  static async getThemes(params: ThemesQuery = {}): Promise<PaginatedResponse<Theme>> {
    const cleanedParams = this.cleanParams(params)
    const response = await httpBase.get(`${this.BASE_PATH}/themes`, { params: cleanedParams })

    // Map _id to id for backward compatibility
    const data = response.data
    if (data.data) {
      data.data = data.data.map((theme: any) => ({
        ...theme,
        id: theme._id || theme.id
      }))
    }

    return data
  }

  /**
   * Get a specific theme by ID with its content sets
   */
  static async getThemeById(
    themeId: string,
    params: Omit<ContentQuery, 'theme_id'> = {}
  ): Promise<PaginatedResponse<ContentSet>> {
    const cleanedParams = this.cleanParams(params)
    const response = await httpBase.get(`${this.BASE_PATH}/themes/${themeId}`, { params: cleanedParams })

    // Map _id to id for backward compatibility
    const data = response.data
    if (data.data) {
      data.data = data.data.map((contentSet: any) => ({
        ...contentSet,
        id: contentSet._id || contentSet.id
      }))
    }

    return data
  }

  /**
   * Get theme details only (without content sets)
   */
  static async getThemeDetails(themeId: string): Promise<ApiResponse<Theme>> {
    const response = await httpBase.get(`${this.BASE_PATH}/theme/${themeId}`)

    // Map _id to id for backward compatibility
    const data = response.data
    if (data.data) {
      data.data = {
        ...data.data,
        id: data.data._id || data.data.id
      }
    }

    return data
  }

  /**
   * Get filtered content sets across all themes
   */
  static async getFilteredContent(params: ContentQuery = {}): Promise<PaginatedResponse<ContentSet>> {
    const cleanedParams = this.cleanParams(params)
    const response = await httpBase.get(`${this.BASE_PATH}/filtered`, { params: cleanedParams })

    // Map _id to id for backward compatibility
    const data = response.data
    if (data.data) {
      data.data = data.data.map((contentSet: any) => ({
        ...contentSet,
        id: contentSet._id || contentSet.id
      }))
    }

    return data
  }



  /**
   * Get filter options for curated content dropdowns
   */
  static async getFilterOptions(): Promise<ApiResponse<FilterOptions>> {
    const response = await httpBase.get(`${this.BASE_PATH}/filter/curated`)
    return response.data
  }

  /**
   * Get filter options for themes dropdowns
   */
  static async getThemeFilterOptions(): Promise<ApiResponse<ThemeFilterOptions>> {
    const response = await httpBase.get(`${this.BASE_PATH}/filters/themes`)
    return response.data
  }

  /**
   * Get questions for a specific curated content set
   */
  static async getQuestions(curatedContentId: string): Promise<Question[]> {
    const response = await httpBase.get(`${this.BASE_PATH}/questions/${curatedContentId}`)
    return response.data
  }

  /**
   * Convert curated content set to task set
   */
  static async convertToTaskSet(curatedContentSetId: string): Promise<ApiResponse<{ task_set_id: string; message: string; status: string }>> {
    const response = await httpBase.post(`${this.BASE_PATH}/convert/${curatedContentSetId}`)
    return response
  }

  /**
   * Get today's theme
   */
  static async getTodaysTheme(): Promise<ApiResponse<{
    curated_set_id: string;
    task_set_id: string;
    theme_color: string;
    title: string;
    theme_id: string;
    engagement_questions?: EngagementQuestion[];
    description?: string;
    description_en?: string;
  }>> {
    const response = await httpBase.get(`${this.BASE_PATH}/get_todays_theme`)
    return response.data
  }

  /**
   * Create a new theme (Management API)
   */
  static async createTheme(themeData: CreateThemeRequest): Promise<ApiResponse<Theme>> {
    const response = await httpBase.post('/v1/management/curated/themes', themeData)
    return response.data
  }

  /**
   * Update an existing theme (Management API)
   */
  static async updateTheme(themeId: string, themeData: UpdateThemeRequest): Promise<ApiResponse<Theme>> {
    const response = await httpBase.put(`/v1/management/curated/themes/${themeId}`, themeData)
    return response.data
  }

  /**
   * Delete a theme (Management API)
   */
  static async deleteTheme(themeId: string): Promise<ApiResponse<{ message: string }>> {
    const response = await httpBase.delete(`/v1/management/curated/themes/${themeId}`)
    return response.data
  }
}

/**
 * Export individual methods for easier importing
 */
export const {
  getThemes,
  getThemeById,
  getThemeDetails,
  getFilteredContent,
  getFilterOptions,
  getThemeFilterOptions,
  getQuestions,
  convertToTaskSet,
  getTodaysTheme,
  createTheme,
  updateTheme,
  deleteTheme
} = CuratedService
